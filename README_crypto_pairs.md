# 加密货币永续合约交易对获取工具

## 概述

`get_crypto_top_pairs.py` 是一个专注于永续合约的加密货币交易对获取工具，支持 Binance 和 OKX 两大主流交易所。该工具提供统一的接口来获取永续合约交易对数据，特别适合FreqTrade用户。

## 主要功能

### ✅ 整合的优点

**来自 get_binance_top_pairs.py 的优点：**
- 完整的类架构和错误处理
- 支持多种排序方式（市值、成交量）
- 支持多种计价货币和时间周期
- 完整的命令行参数支持
- 币名修正功能
- 多种输出格式
- 代理支持

**来自 get_okx_top_pairs.py 的优点：**
- OKX永续合约支持
- FreqTrade格式输出（XXX/USDT:USDT）
- 简洁的市值集成
- 专门针对永续合约优化

### 🚀 核心功能

1. **多交易所支持**
   - Binance（币安）永续合约
   - OKX 永续合约

2. **专注永续合约**
   - 仅支持永续合约交易
   - 优化的永续合约数据处理

3. **多种排序方式**
   - 按市值排序（默认）
   - 按成交量排序

4. **统一的市值数据**
   - 集成CoinGecko API
   - 统一的市值显示格式

5. **FreqTrade格式输出**
   - 永续合约：`ETH/USDT:USDT`

## 使用方法

### 基本用法

```bash
# 默认：Binance永续合约按市值排序前30个USDT交易对
python3 get_crypto_top_pairs.py

# OKX永续合约按市值排序
python3 get_crypto_top_pairs.py -e okx

# Binance永续合约按成交量排序
python3 get_crypto_top_pairs.py --sort-by volume

# 获取前50个交易对
python3 get_crypto_top_pairs.py -n 50

# BTC计价的永续合约
python3 get_crypto_top_pairs.py -q BTC
```

### 高级用法

```bash
# Binance永续合约，按成交量排序，前20个
python3 get_crypto_top_pairs.py --sort-by volume -n 20

# OKX永续合约，BTC计价，按成交量排序，前20个
python3 get_crypto_top_pairs.py -e okx -q BTC --sort-by volume -n 20

# 仅输出JSON格式
python3 get_crypto_top_pairs.py --json-only

# 输出config.json格式（用于FreqTrade配置）
python3 get_crypto_top_pairs.py --config-format
```

### 参数说明

| 参数 | 简写 | 默认值 | 说明 |
|------|------|--------|------|
| `--exchange` | `-e` | `binance` | 交易所选择（binance/okx） |
| `--number` | `-n` | `30` | 获取前N个交易对 |
| `--quote-currency` | `-q` | `USDT` | 计价货币 |
| `--sort-by` | - | `market_cap` | 排序方式（market_cap/volume） |
| `--json-only` | - | - | 仅输出JSON格式 |
| `--config-format` | - | - | 输出config.json格式 |

## 输出格式

### 表格格式（默认）

```
🏆 币安永续合约24h内成交量最高的5个USDT交易对
=======================================================================================
排名 交易对           24h交易量(USDT)      市值            最新价格        24h涨跌幅
---------------------------------------------------------------------------------------
1    ETH/USDT:USDT    26.70B               $420.70B        3481.090000     -3.82%
2    XRP/USDT:USDT    3.06B                $175.15B        2.939300        -0.31%
3    SOL/USDT:USDT    4.43B                $87.68B         162.540000      -3.40%
```

### JSON格式列表

```json
["ETH/USDT:USDT","XRP/USDT:USDT","SOL/USDT:USDT","TRX/USDT:USDT","DOGE/USDT:USDT"]
```

### Config格式（FreqTrade）

```json
[
  "ETH/USDT:USDT",
  "XRP/USDT:USDT",
  "SOL/USDT:USDT"
]
```

## 依赖要求

```bash
# 基础依赖（所有功能都使用requests库）
pip install requests
```

## 特性对比

| 功能 | 原Binance脚本 | 原OKX脚本 | 永续合约版 |
|------|---------------|-----------|------------|
| Binance永续合约 | ❌ | ❌ | ✅ |
| OKX永续合约 | ❌ | ✅ | ✅ |
| 市值排序 | ✅ | ✅ | ✅ |
| 成交量排序 | ✅ | ✅ | ✅ |
| 多计价货币 | ✅ | ❌ | ✅ |
| 命令行参数 | ✅ | ❌ | ✅ |
| FreqTrade格式 | ✅ | ✅ | ✅ |
| 币名修正 | ✅ | ❌ | ✅ |
| 代理支持 | ✅ | ❌ | ✅ |
| 无额外依赖 | ❌ | ✅ | ✅ |

## 使用场景

1. **FreqTrade用户**：获取永续合约交易对配置
2. **量化交易**：按市值或成交量筛选活跃交易对
3. **市场分析**：比较不同交易所的交易对活跃度
4. **自动化脚本**：集成到交易策略中自动更新交易对列表

## 注意事项

1. **API限制**：请注意各交易所的API调用频率限制
2. **网络环境**：支持代理设置，适应不同网络环境
3. **数据准确性**：市值数据来源于CoinGecko，可能存在延迟
4. **依赖管理**：所有功能都使用requests库，无需额外依赖

## 更新记录

- **2025-08-02**：创建整合版本，结合两个脚本的所有优点
- **2025-08-02**：删除现货交易功能，专注永续合约
- 支持多交易所永续合约、统一市值数据
- 简化依赖，仅使用requests库
